package entities

import (
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
)

// SessionStatus represents the current state of a WhatsApp session
type SessionStatus string

const (
	// SessionStatusInitialized means the session is created but not yet connected
	SessionStatusInitialized SessionStatus = "initialized"
	// SessionStatusConnecting means the session is in the process of connecting
	SessionStatusConnecting SessionStatus = "connecting"
	// SessionStatusConnected means the session is successfully connected
	SessionStatusConnected SessionStatus = "connected"
	// SessionStatusPaused means the session is temporarily paused
	SessionStatusPaused SessionStatus = "paused"
	// SessionStatusDisconnected means the session is disconnected
	SessionStatusDisconnected SessionStatus = "disconnected"
	// SessionStatusFailed means the session failed to connect or encountered an error
	SessionStatusFailed SessionStatus = "failed"
)

// Session represents a WhatsApp session
type Session struct {
	Base
	// RegID is the registration ID of the WhatsApp device
	RegID string `json:"reg_id"`
	// JID is the WhatsApp JID (Jabber ID)
	JID string `json:"jid" gorm:"column:jid"`
	// Status represents the current state of the session
	Status SessionStatus `json:"status"`
	// LastConnected is the timestamp of the last successful connection
	LastConnected time.Time `json:"last_connected"`
	// LastDisconnected is the timestamp of the last disconnection
	LastDisconnected time.Time `json:"last_disconnected"`
	// ConnectionAttempts tracks how many times we've tried to connect
	ConnectionAttempts int `json:"connection_attempts"`
	// ErrorMessage stores the last error message if any
	ErrorMessage string `json:"error_message"`
	// ProxyUsed indicates if a proxy was used for this session
	ProxyUsed bool `json:"proxy_used"`
	// ProxyAddress stores the proxy address if used
	ProxyAddress string `json:"proxy_address"`
	// DeviceInfo stores device information used for this session
	ProxyID    uuid.UUID `json:"proxy_id"`
	DeviceInfo string    `json:"device_info"`
	// AutoReconnect indicates if the session should automatically reconnect
	AutoReconnect bool `json:"auto_reconnect" gorm:"default:true"`
	// MessageCount tracks the number of messages sent in this session
	MessageCount int    `json:"message_count" gorm:"default:0"`
	Country      string `json:"country" example:"TR"`
	IP           string `json:"ip" gorm:"column:ip"`
	Timezone     string `json:"timezone" gorm:"column:timezone"`
}

// SessionEvent represents an event that occurred during a session
type SessionEvent struct {
	Base
	// SessionID links to the session
	SessionID uuid.UUID `json:"session_id" gorm:"type:uuid"`
	// EventType describes what kind of event occurred
	EventType string `json:"event_type"`
	// Description provides details about the event
	Description string `json:"description"`
	// Timestamp records when the event occurred
	Timestamp time.Time `json:"timestamp"`
}

// SessionSubscription represents a subscription to presence updates
type SessionSubscription struct {
	Base
	// SessionID links to the session
	SessionID uuid.UUID `json:"session_id" gorm:"type:uuid"`
	// Phone is the phone number being subscribed to
	Phone string `json:"phone"`
	// JID is the WhatsApp JID being subscribed to
	JID string `json:"jid" gorm:"column:jid"`
	// Active indicates if the subscription is currently active
	Active         bool   `json:"active" gorm:"default:true"`
	EventHandlerId uint32 `json:"event_handler_id"`
}

// WhatsmeowWebsocketError represents the structure of whatsmeow_websocket_errors table
type WhatsmeowWebsocketError struct {
	ID        int    `json:"id" gorm:"primaryKey;autoIncrement"`
	ClientJID string `json:"client_jid" gorm:"column:client_jid"`
	Timestamp int64  `json:"timestamp" gorm:"column:timestamp"`
	Error     string `json:"error" gorm:"column:error"`
	Processed bool   `json:"processed" gorm:"column:processed;default:false"`
}

// TableName specifies the table name for GORM
func (WhatsmeowWebsocketError) TableName() string {
	return "whatsmeow_websocket_errors"
}

// Helper functions to map entities to DTOs
func (session *Session) MapToSessionResponse() dtos.SessionResponse {
	return dtos.SessionResponse{
		ID:                 session.ID,
		RegID:              session.RegID,
		JID:                session.JID,
		Status:             string(session.Status),
		LastConnected:      session.LastConnected,
		LastDisconnected:   session.LastDisconnected,
		ConnectionAttempts: session.ConnectionAttempts,
		ErrorMessage:       session.ErrorMessage,
		ProxyUsed:          session.ProxyUsed,
		AutoReconnect:      session.AutoReconnect,
		MessageCount:       session.MessageCount,
		CreatedAt:          session.CreatedAt,
		IP:                 session.IP,
	}
}
