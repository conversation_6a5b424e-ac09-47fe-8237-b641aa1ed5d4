package session

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/nat"
	"github.com/sayeworldevelopment/wp-core/pkg/proxy"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type Service interface {
	CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error)
	GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error)
	DeleteSession(ctx context.Context, id uuid.UUID) error
	ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error)
	ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error)
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error)
	SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error
	RemovePresenceSubscription(ctx context.Context, sessionID uuid.UUID, phone string) error
	GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error)
	GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error)
	GetLastPresenceByPhone(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceItem, error)
	GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error)
	GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
	UpdateSessionWithRegID(ctx context.Context, sessionID uuid.UUID, regID string) (dtos.SessionResponse, error)
}

// Helper functions to map entities to DTOs
func mapToSessionResponse(session entities.Session) dtos.SessionResponse {
	return dtos.SessionResponse{
		ID:                 session.ID,
		RegID:              session.RegID,
		JID:                session.JID,
		Status:             string(session.Status),
		LastConnected:      session.LastConnected,
		LastDisconnected:   session.LastDisconnected,
		ConnectionAttempts: session.ConnectionAttempts,
		ErrorMessage:       session.ErrorMessage,
		ProxyUsed:          session.ProxyUsed,
		AutoReconnect:      session.AutoReconnect,
		MessageCount:       session.MessageCount,
		CreatedAt:          session.CreatedAt,
	}
}

func mapToSessionEventResponse(event entities.SessionEvent) dtos.SessionEventResponse {
	return dtos.SessionEventResponse{
		ID:          event.ID,
		SessionID:   event.SessionID,
		EventType:   event.EventType,
		Description: event.Description,
		Timestamp:   event.Timestamp,
	}
}

type service struct {
	repo Repository
	wp   *wrapper.Client
}

// NewSessionService creates a new session service
func NewService(sessionRepo Repository, wp *wrapper.Client) Service {
	return &service{
		repo: sessionRepo,
		wp:   wp,
	}
}

func (s *service) CreateSession(ctx context.Context, req dtos.CreateSessionReq) (dtos.SessionResponse, error) {
	var session entities.Session

	// default value
	if req.Timezone == "" {
		req.Timezone = "Europe/Istanbul"
	}

	if req.UseProxy {
		proxy_id, proxy_address, err := proxy.SetProxyInfoForSession(ctx, req.IP, req.E164PhoneNumber)
		if err != nil {
			return dtos.SessionResponse{}, errors.New("failed to set proxy: " + err.Error())
		}

		country, _ := proxy.DetectCountryByPhone(req.E164PhoneNumber)

		session = entities.Session{
			RegID:              "", // Will be set when login code is obtained
			JID:                "", // Will be set when login code is obtained
			Status:             entities.SessionStatusInitialized,
			ConnectionAttempts: 0,
			ProxyUsed:          req.UseProxy,
			ProxyAddress:       proxy_address,
			ProxyID:            proxy_id,
			DeviceInfo:         req.DeviceInfo,
			AutoReconnect:      req.AutoReconnect,
			Country:            country,
			IP:                 req.IP,
			Timezone:           req.Timezone,
		}
	} else {
		session = entities.Session{
			RegID:              "", // Will be set when login code is obtained
			JID:                "", // Will be set when login code is obtained
			Status:             entities.SessionStatusInitialized,
			ConnectionAttempts: 0,
			ProxyUsed:          req.UseProxy,
			DeviceInfo:         req.DeviceInfo,
			AutoReconnect:      req.AutoReconnect,
			IP:                 req.IP,
			Timezone:           req.Timezone,
		}
	}

	createdSession, err := s.repo.CreateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	event := entities.SessionEvent{
		SessionID:   createdSession.ID,
		EventType:   "created",
		Description: "Session created",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(createdSession), nil
}

func (s *service) GetSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}
	return mapToSessionResponse(session), nil
}

func (s *service) UpdateSession(ctx context.Context, req dtos.UpdateSessionReq) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, req.ID)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update fields if provided
	if req.Status != "" {
		session.Status = entities.SessionStatus(req.Status)
	}

	if req.AutoReconnect != nil {
		session.AutoReconnect = *req.AutoReconnect
	}

	if req.ProxyAddress != "" {
		session.ProxyAddress = req.ProxyAddress
		session.ProxyUsed = true
	}

	// Save the updated session
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session update event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "updated",
		Description: "Session updated",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) DeleteSession(ctx context.Context, id uuid.UUID) error {
	// Get the session first to check if it exists
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return err
	}

	// Clean up all presence subscriptions first
	err = s.cleanupSessionSubscriptions(ctx, id)
	if err != nil {
		fmt.Printf("Warning: Failed to cleanup subscriptions for session %s: %v\n", id, err)
		// Continue with deletion even if cleanup fails
	}

	// Disconnect the session if it's connected
	if session.Status == entities.SessionStatusConnected {
		_, err = s.DisconnectSession(ctx, id)
		if err != nil {
			return err
		}
	}

	// Delete the session
	return s.repo.DeleteSession(ctx, id)
}

func (s *service) ListSessions(ctx context.Context, status string, page, perPage int) ([]dtos.SessionResponse, int64, error) {
	offset := (page - 1) * perPage
	sessions, count, err := s.repo.ListSessions(ctx, status, perPage, offset)
	if err != nil {
		return nil, 0, err
	}

	var sessionResponses []dtos.SessionResponse
	for _, session := range sessions {
		sessionResponses = append(sessionResponses, mapToSessionResponse(session))
	}

	return sessionResponses, count, nil
}

func (s *service) ConnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Update session status to connecting
	session.Status = entities.SessionStatusConnecting
	session.ConnectionAttempts++
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection attempt event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connecting",
		Description: fmt.Sprintf("Connection attempt #%d", session.ConnectionAttempts),
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	// Get the device from whatsmeow
	senderArr := strings.Split(session.JID, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := s.wp.MContainer.GetDevice(ctx, sender)
	if err != nil || device == nil {
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, get device err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      "unknown", // IP is not available in this context
			})
		}

		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Device not found:"
		s.repo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Device not found:",
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), errors.New("device not found")
	}

	// Create WhatsApp client
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)

	// Add event handler
	client.AddEventHandler(func(evt interface{}) {
		// Handle events and record them
		s.handleWhatsAppEvent(ctx, session.ID, evt)
	})

	// Connect to WhatsApp
	client.EnableAutoReconnect = true
	err = client.Connect()
	if err != nil {
		session.Status = entities.SessionStatusFailed
		session.ErrorMessage = "Connection failed: " + err.Error()
		s.repo.UpdateSession(ctx, session)

		failEvent := entities.SessionEvent{
			SessionID:   session.ID,
			EventType:   "error",
			Description: "Connection failed: " + err.Error(),
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, failEvent)

		return mapToSessionResponse(session), err
	}

	// Client is already stored in wrapper's activeClients map

	// Update session status to connected
	session.Status = entities.SessionStatusConnected
	session.LastConnected = time.Now()
	session.ErrorMessage = ""
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record connection success event
	successEvent := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "connected",
		Description: "Successfully connected to WhatsApp",
		Timestamp:   time.Now(),
	}

	s.repo.CleanWhatsmeowTables(ctx, session.JID)

	s.repo.RecordSessionEvent(ctx, successEvent)

	return session.MapToSessionResponse(), nil
}

func (s *service) DisconnectSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Clean up all presence subscriptions before disconnecting
	err = s.cleanupSessionSubscriptions(ctx, id)
	if err != nil {
		fmt.Printf("Warning: Failed to cleanup subscriptions for session %s: %v\n", id, err)
		// Continue with disconnection even if cleanup fails
	}

	// Get the client from the global map
	client := nat.WaConnects[session.RegID]
	if client != nil {
		// Disconnect the client
		client.Disconnect()
		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}

	// Update session status
	session.Status = entities.SessionStatusDisconnected
	session.LastDisconnected = time.Now()
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record disconnection event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "disconnected",
		Description: "Disconnected from WhatsApp and cleaned up subscriptions",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return session.MapToSessionResponse(), nil
}

func (s *service) PauseSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only pause connected sessions
	if session.Status != entities.SessionStatusConnected {
		return mapToSessionResponse(session), errors.New("can only pause connected sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusPaused
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record pause event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "paused",
		Description: "Session paused",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) ResumeSession(ctx context.Context, id uuid.UUID) (dtos.SessionResponse, error) {
	session, err := s.repo.GetSessionByID(ctx, id)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Can only resume paused sessions
	if session.Status != entities.SessionStatusPaused {
		return mapToSessionResponse(session), errors.New("can only resume paused sessions")
	}

	// Update session status
	session.Status = entities.SessionStatusConnected
	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record resume event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "resumed",
		Description: "Session resumed",
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

func (s *service) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, page, perPage int) ([]dtos.SessionEventResponse, error) {
	offset := (page - 1) * perPage
	events, err := s.repo.GetSessionEvents(ctx, sessionID, perPage, offset)
	if err != nil {
		return nil, err
	}

	var eventResponses []dtos.SessionEventResponse
	for _, event := range events {
		eventResponses = append(eventResponses, mapToSessionEventResponse(event))
	}

	return eventResponses, nil
}

func (s *service) SubscribePresence(ctx context.Context, req dtos.SessionSubscriptionReq) error {
	session, err := s.repo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return err
	}

	// Can only subscribe with connected sessions
	if session.Status != entities.SessionStatusConnected {
		return errors.New("can only subscribe with connected sessions")
	}

	device, err := s.repo.FindActiveDeviceByRegID(ctx, session.RegID)
	if err != nil || device.RegistrationID == "" {
		return err
	}

	isExist := s.repo.SubscribeIsExist(ctx, session.ID.String(), req.Phone)
	if isExist {
		log.CreateLog(&entities.Log{
			Title:   "Subscribe Presence Failed",
			Message: "Subscribe Presence Failed, subscribe is exist",
			Entity:  "session",
			Type:    "error",
		})
		return errors.New("subscribe is exist")
	}

	_, err = s.wp.SubscribePresence(ctx, device.JID, req.Phone)
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Subscribe Presence Failed",
			Message: "Subscribe Presence Failed, subscribe presence err:" + err.Error(),
			Entity:  "session",
			Type:    "error",
		})
		return err
	}

	// Record subscription event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "subscribed",
		Description: "Subscribed to presence updates for " + req.Phone,
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return nil
}

func (s *service) RemovePresenceSubscription(ctx context.Context, sessionID uuid.UUID, phone string) error {
	// Get the subscription to find the event handler ID
	subscription, err := s.repo.GetSubscriptionBySessionAndPhone(ctx, sessionID, phone)
	if err != nil {
		return fmt.Errorf("subscription not found: %w", err)
	}

	// Get the session to find the JID
	session, err := s.repo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("session not found: %w", err)
	}

	// Remove the event handler from WhatsApp client
	err = s.wp.RemovePresenceSubscription(ctx, session.JID, phone, subscription.EventHandlerId)
	if err != nil {
		return fmt.Errorf("failed to remove WhatsApp subscription: %w", err)
	}

	// Delete the subscription from database
	err = s.repo.DeleteSubscription(ctx, subscription.ID)
	if err != nil {
		return fmt.Errorf("failed to delete subscription from database: %w", err)
	}

	// Record removal event
	event := entities.SessionEvent{
		SessionID:   sessionID,
		EventType:   "unsubscribed",
		Description: "Removed presence subscription for " + phone,
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return nil
}

// cleanupSessionSubscriptions removes all presence subscriptions for a session
func (s *service) cleanupSessionSubscriptions(ctx context.Context, sessionID uuid.UUID) error {
	// Get all subscriptions for this session
	subscriptions, err := s.repo.GetSubscriptions(ctx, sessionID)
	if err != nil {
		return err
	}

	// Get session info for JID
	session, err := s.repo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return err
	}

	// Remove each subscription
	for _, subscription := range subscriptions {
		if subscription.Active && subscription.EventHandlerId > 0 {
			// Remove from WhatsApp client
			err := s.wp.RemovePresenceSubscription(ctx, session.JID, subscription.Phone, subscription.EventHandlerId)
			if err != nil {
				fmt.Printf("Failed to remove WhatsApp subscription for phone %s: %v\n", subscription.Phone, err)
				// Continue with other subscriptions even if one fails
			}

		}
		err = s.repo.DeleteSubscription(ctx, subscription.ID)
		if err != nil {
			fmt.Printf("Failed to delete subscription from database for phone %s: %v\n", subscription.Phone, err)
		}
	}

	return nil
}

func (s *service) GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	return s.repo.GetSubscriptions(ctx, sessionID)
}

func (s *service) GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error) {
	return s.repo.GetPresences(ctx, sessionID, page, perPage)
}

// UpdateSessionWithRegID updates a session with registration ID and JID
func (s *service) UpdateSessionWithRegID(ctx context.Context, sessionID uuid.UUID, regID string) (dtos.SessionResponse, error) {
	// Get the session
	session, err := s.repo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	session.RegID = regID

	err = s.repo.UpdateSession(ctx, session)
	if err != nil {
		return dtos.SessionResponse{}, err
	}

	// Record session update event
	event := entities.SessionEvent{
		SessionID:   session.ID,
		EventType:   "reg_id_updated",
		Description: "Session updated with registration ID: " + regID,
		Timestamp:   time.Now(),
	}
	s.repo.RecordSessionEvent(ctx, event)

	return mapToSessionResponse(session), nil
}

// handleWhatsAppEvent handles WhatsApp events and records them in the session events
func (s *service) handleWhatsAppEvent(ctx context.Context, sessionID uuid.UUID, evt interface{}) {
	// Record different types of events based on the event type
	// This is a simplified version, you can expand it to handle more event types
	switch evt.(type) {
	case *events.Disconnected:
		// Handle disconnection event
		session, err := s.repo.GetSessionByID(ctx, sessionID)
		if err != nil {
			return
		}

		session.Status = entities.SessionStatusDisconnected
		session.LastDisconnected = time.Now()
		s.repo.UpdateSession(ctx, session)

		event := entities.SessionEvent{
			SessionID:   sessionID,
			EventType:   "disconnected",
			Description: "Disconnected from WhatsApp",
			Timestamp:   time.Now(),
		}
		s.repo.RecordSessionEvent(ctx, event)

		// Remove from the global map
		delete(nat.WaConnects, session.RegID)
	}
}

func (s *service) GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error) {
	return s.repo.GetCurrentPresences(ctx, sessionID, phone)
}

func (s *service) GetLastPresenceByPhone(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceItem, error) {
	return s.repo.GetLastPresenceByPhone(ctx, sessionID, phone)
}

func (s *service) GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error) {
	return s.repo.GetPresenceHistory(ctx, sessionID, req)
}

func (r *sessionRepository) SubscribeIsExist(ctx context.Context, sessionId, phone string) bool {

	var subscribe entities.SessionSubscription
	_ = r.db.WithContext(ctx).Where("session_id = ? AND phone = ?", sessionId, phone).First(&subscribe).Error
	if subscribe.ID != uuid.Nil {
		return true
	}
	return false
}
